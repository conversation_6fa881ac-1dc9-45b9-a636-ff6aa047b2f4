'use client'

import React, { useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import type { InterfaceConfig } from '@/types/data'
import { Plus, Trash2, Info } from 'lucide-react'

// 用于稳定渲染的映射项类型
interface MappingItem {
  id: string
  key: string
  value: string
}

interface RegexItem {
  id: string
  key: string
  pattern: string
}

interface ParsingRulesProps {
  method: 'GET' | 'POST'
  rules: InterfaceConfig['parsingRules']
  onChange: (rules: InterfaceConfig['parsingRules']) => void
}

export function ParsingRules({
  method,
  rules,
  onChange
}: ParsingRulesProps) {
  const { t } = useTypedTranslation('config')

  // 将对象转换为带有稳定ID的数组
  const mappingItems = useMemo((): MappingItem[] => {
    const mapping = rules?.variableMapping || {}
    return Object.entries(mapping).map(([key, value], index) => ({
      id: `mapping-${key}-${index}`, // 使用键名和索引生成稳定ID
      key,
      value
    }))
  }, [rules?.variableMapping])

  const regexItems = useMemo((): RegexItem[] => {
    const patterns = rules?.regexPatterns || {}
    return Object.entries(patterns).map(([key, pattern], index) => ({
      id: `regex-${key}-${index}`, // 使用键名和索引生成稳定ID
      key,
      pattern
    }))
  }, [rules?.regexPatterns])

  // 获取方法颜色（与ConfigList保持一致）
  const getMethodColor = (method: string) => {
    const colors = {
      'GET': 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800',
      'POST': 'bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
      'PUT': 'bg-orange-50 text-orange-700 border border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800',
      'DELETE': 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
      'PATCH': 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800'
    }
    return colors[method as keyof typeof colors] || 'bg-gray-50 text-gray-700 border border-gray-200'
  }  

  // 当请求方式变化时自动更新规则类型
  const getRuleType = () => {
    if (method === 'GET') return 'get'
    return rules?.type === 'get' ? 'post-json' : rules?.type || 'post-json'
  }

  const currentRuleType = getRuleType()

  // 更新规则类型
  const handleTypeChange = (type: 'post-form' | 'post-multipart' | 'post-json' | 'post-plain') => {
    onChange({
      ...(rules || {}),
      type,
      variableMapping: type === 'post-plain' ? {} : (rules?.variableMapping || {}),
      regexPatterns: type === 'post-plain' ? (rules?.regexPatterns || {}) : undefined
    })
  }

  // 添加参数映射
  const handleAddMapping = () => {
    const newRules = { ...(rules?.variableMapping || {}) }
    const existingNumbers = Object.keys(newRules)
      .filter(key => key.startsWith(t('newParam')))
      .map(key => parseInt(key.replace(t('newParam'), '')))
      .filter(num => !isNaN(num))
    const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1
    newRules[`${t('newParam')}${nextNumber}`] = `${t('variable')}${nextNumber}`
    onChange({ ...(rules || {}), variableMapping: newRules })
  }

  // 更新参数映射
  const handleMappingChange = (itemId: string, newKey: string, newValue: string) => {
    const item = mappingItems.find(item => item.id === itemId)
    if (!item) return

    const newRules = { ...(rules?.variableMapping || {}) }

    // 删除旧键
    delete newRules[item.key]
    // 添加新键值对
    newRules[newKey] = newValue

    onChange({ ...(rules || {}), variableMapping: newRules })
  }

  // 删除参数映射
  const handleDeleteMapping = (itemId: string) => {
    const item = mappingItems.find(item => item.id === itemId)
    if (!item) return

    const newRules = { ...(rules?.variableMapping || {}) }
    delete newRules[item.key]
    onChange({ ...(rules || {}), variableMapping: newRules })
  }

  // 添加正则表达式规则
  const handleAddRegexPattern = () => {
    const newPatterns = { ...(rules?.regexPatterns || {}) }
    const existingNumbers = Object.keys(newPatterns)
      .filter(key => key.startsWith(t('variable')))
      .map(key => parseInt(key.replace(t('variable'), '')))
      .filter(num => !isNaN(num))
    const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1
    newPatterns[`${t('variable')}${nextNumber}`] = `正则表达式${nextNumber}`
    onChange({ ...(rules || {}), regexPatterns: newPatterns })
  }

  // 更新正则表达式规则
  const handleRegexPatternChange = (itemId: string, newKey: string, newPattern: string) => {
    const item = regexItems.find(item => item.id === itemId)
    if (!item) return

    const newPatterns = { ...(rules?.regexPatterns || {}) }

    // 删除旧键
    delete newPatterns[item.key]
    // 添加新键值对
    newPatterns[newKey] = newPattern

    onChange({ ...(rules || {}), regexPatterns: newPatterns })
  }

  // 删除正则表达式规则
  const handleDeleteRegexPattern = (itemId: string) => {
    const item = regexItems.find(item => item.id === itemId)
    if (!item) return

    const newPatterns = { ...(rules?.regexPatterns || {}) }
    delete newPatterns[item.key]
    onChange({ ...(rules || {}), regexPatterns: newPatterns })
  }

  return (
    <div className="space-y-6">
      {/* 请求类型说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            {t('messageExtractionRules')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label>{t('requestMethod')}</Label>
              <div className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium ${getMethodColor(method)}`}>
                {method}
              </div>
            </div>

            {method === 'POST' && (
              <div className="space-y-2">
                <Label>{t('contentType')}</Label>
                <Select 
                  value={currentRuleType} 
                  onValueChange={handleTypeChange}
                >
                  <SelectTrigger className="w-full cursor-pointer">
                    <SelectValue>
                      {currentRuleType === 'post-form' && 'application/x-www-form-urlencoded'}
                      {currentRuleType === 'post-multipart' && 'multipart/form-data'}
                      {currentRuleType === 'post-json' && 'application/json'}
                      {currentRuleType === 'post-plain' && 'text/plain'}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent className="max-w-md">
                    <SelectItem value="post-form">
                      <div className="max-w-xs">
                        <div>application/x-www-form-urlencoded</div>
                        <div className="text-xs text-muted-foreground">{t('formFieldMapping')}</div>
                      </div>
                    </SelectItem>
                    <SelectItem value="post-multipart">
                      <div className="max-w-xs">
                        <div>multipart/form-data</div>
                        <div className="text-xs text-muted-foreground">{t('formFieldMapping')}</div>
                      </div>
                    </SelectItem>
                    <SelectItem value="post-json">
                      <div className="max-w-xs">
                        <div>application/json</div>
                        <div className="text-xs text-muted-foreground">{t('jsonFieldMapping')}</div>
                      </div>
                    </SelectItem>
                    <SelectItem value="post-plain">
                      <div className="max-w-xs">
                        <div>text/plain</div>
                        <div className="text-xs text-muted-foreground">{t('regexExtraction')}</div>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 参数映射配置 (GET/POST-form/POST-json) */}
      {currentRuleType !== 'post-plain' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                {t('variableMapping')}
              </CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddMapping}
              >
                <Plus className="h-4 w-4 mr-1" />
                {t('addMapping')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {mappingItems.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {t('noParameterMapping')}
              </div>
            ) : (
              <div className="space-y-3">
                {mappingItems.map((item) => (
                  <div key={item.id} className="flex items-center gap-2">
                    <div className="flex-1">
                      <Input
                        placeholder={
                          currentRuleType === 'post-json'
                            ? t('jsonPathPlaceholder')
                            : t('interfaceParamPlaceholder')
                        }
                        value={item.key}
                        onChange={(e) => handleMappingChange(item.id, e.target.value, item.value)}
                      />
                    </div>
                    <div className="text-muted-foreground">→</div>
                    <div className="flex-1">
                      <Input
                        placeholder={t('templateVariablePlaceholder')}
                        value={item.value}
                        onChange={(e) => handleMappingChange(item.id, item.key, e.target.value)}
                      />
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteMapping(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 正则表达式配置 (POST-plain) */}
      {currentRuleType === 'post-plain' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>{t('regexRules')}</CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddRegexPattern}
                className='cursor-pointer'
              >
                <Plus className="h-4 w-4 mr-1" />
                {t('addRule')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {regexItems.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {t('noRegexRules')}
              </div>
            ) : (
              <div className="space-y-4">
                {regexItems.map((item, index) => (
                  <div key={item.id} className="p-4 border rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="font-medium">{t('ruleNumber', { number: index + 1 })}</Label>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteRegexPattern(item.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <Label>{t('variableName')}</Label>
                        <Input
                          placeholder={t('templateVariableNamePlaceholder')}
                          value={item.key}
                          onChange={(e) =>
                            handleRegexPatternChange(item.id, e.target.value, item.pattern)
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>{t('regexExpression')}</Label>
                        <Input
                          placeholder={t('regexPatternPlaceholder')}
                          value={item.pattern}
                          onChange={(e) =>
                            handleRegexPatternChange(item.id, item.key, e.target.value)
                          }
                          className="font-mono text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}